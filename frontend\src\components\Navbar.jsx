import React from 'react'
import {useAuthStore} from '../store/useAuthStore.js';
import { Link } from 'react-router-dom';
import { MessageSquare } from 'lucide-react';
import { Settings, User, LogOut } from 'lucide-react';

const Navbar = () => {
  const {logout , authUser} = useAuthStore();
  return (
    <header className=" border-b border-base-300 fixed w-full top-0 z-40 backdrop-blur-lg bg-base-100/80">
      <div className="container mx-auto px-4 h-16">
        <div className="flex justify-between h-full items-center">
          <div className="flex items-center gap-8">
            <Link
              to="/"
              className="flex items-center gap-2.5 hover:opacity-70 transition-all "
            >
              <div className="size-8 rounded-lg bg-primary/10 flex justify-center items-center">
                <MessageSquare className="size-5 text-primary" />
              </div>
              <h1 className="text-lg font-bold"> Chatty</h1>
            </Link>
          </div>
          <div className="flex items-center gap-2">
            <Link
              to={"/settings"}
              className="btn btn-sm gap-2 transition-colors"
            >
              <Settings className="size-5 " />
              <span className="hidden sm:inline">Settings</span>
            </Link>
            {authUser && (
              <>
                <Link
                  to={"/profile"}
                  className="btn btn-sm gap-2 transition-colors"
                >
                  <User className="size-5 " />
                  <span className="hidden sm:inline">Profile</span>
                </Link>
                <button
                  className="btn btn-sm gap-2 transition-colors"
                  onClick={logout}
                >
                  <LogOut className="size-5 " />
                  <span className="hidden sm:inline">Logout</span>
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

export default Navbar;